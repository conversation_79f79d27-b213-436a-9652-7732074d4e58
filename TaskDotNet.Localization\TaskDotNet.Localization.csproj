<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Update="SharedResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>SharedResource.resx</DependentUpon>
    </Compile>
    <Compile Update="TermsAndConditions\TermsAndConditions.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>TermsAndConditions.resx</DependentUpon>
    </Compile>
    <Compile Update="PrivacyPolicy\PrivacyPolicy.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>PrivacyPolicy.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="SharedResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>SharedResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="TermsAndConditions\TermsAndConditions.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>TermsAndConditions.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="PrivacyPolicy\PrivacyPolicy.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>PrivacyPolicy.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
